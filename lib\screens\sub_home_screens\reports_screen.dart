import 'package:flutter/material.dart';
import 'package:moodvibe/controllers/mood_controller.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/journal_contoller.dart';
import '../../theme/app_theme.dart';
import '../../widgets/activities_bar_chart_widget.dart';
import '../../widgets/activities_list_widget.dart';
import '../../widgets/activities_pie_chart_widget.dart';
import '../../widgets/journal_count_widget.dart';
import '../../widgets/mood_count_summary_widget.dart';
import '../../widgets/mood_line_chart_widget.dart';
import '../../widgets/weekly_mood_list_widget.dart';
import '../../widgets/monthly_mood_list_widget.dart';

class ReportsScreen extends StatelessWidget with WatchItMixin {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final reports = watchIt<MoodController>();
    final journal = watchIt<JournalController>();
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Reports'), centerTitle: true),
      body: ListView(
        padding: const EdgeInsets.only(
          top: 16,
          left: 16,
          right: 16,
          bottom: 80,
        ),
        children: [
          MoodLineChartWidget(
            moods: reports.moods,
            daysToShow: 10, // Optional, defaults to 10
          ),
          const SizedBox(height: 16),
          MoodCountSummaryWidget(moods: reports.moods),
          const SizedBox(height: 16),
          WeeklyMoodListWidget(moods: reports.moods),
          const SizedBox(height: 16),
          MonthlyMoodListWidget(moods: reports.moods),
          const SizedBox(height: 16),
          JournalCountWidget(
            journals: journal.journals, // or however you access journals
          ),
          const SizedBox(height: 16),
          ActivitiesBarChartWidget(moods: reports.moods, daysToShow: 10),
          const SizedBox(height: 16),
          ActivitiesPieChartWidget(moods: reports.moods, daysToShow: 10),
          const SizedBox(height: 16),
          ActivitiesListWidget(moods: reports.moods, daysToShow: 10),
          const SizedBox(height: 16),

          // Add more reports as needed
          // ...
        ],
      ),
    );
  }
}
