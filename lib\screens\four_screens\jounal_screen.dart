import 'dart:io';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/journal_contoller.dart';
import '../../theme/app_theme.dart';
import '../../theme/icon_size.dart';

class JounalScreen extends WatchingStatefulWidget {
  const JounalScreen({super.key});

  @override
  State<JounalScreen> createState() => _JounalScreenState();
}

class _JounalScreenState extends State<JounalScreen> {
  @override
  Widget build(BuildContext context) {
    final journal = watchIt<JournalController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(
        title: const Text('My Journal'),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              context.push('/settings');
            },
            icon: const Icon(Icons.settings, size: AppIconSize.appIconSize),
          ),
        ],
      ),
      body:
          journal.isLoading
              ? const Center(child: CircularProgressIndicator())
              : journal.journals.isEmpty
              ? _buildEmptyState(context)
              : _buildGroupedJournalList(journal),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/addJournalEntry');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildGroupedJournalList(JournalController journal) {
    // Group journals by date
    final groupedJournals = _groupJournalsByDate(journal.journals);
    final sortedDates =
        groupedJournals.keys.toList()
          ..sort((a, b) => b.compareTo(a)); // Most recent first

    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.only(top: 16, left: 16, right: 16, bottom: 80),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final date = sortedDates[index];
        final journalsForDate = groupedJournals[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Day header with beautiful styling
            _buildDayHeader(date, journalsForDate.length),
            const SizedBox(height: 12),

            // Journal entries for this day
            ...journalsForDate.map(
              (journalEntry) => _buildJournalCard(journalEntry),
            ),

            const SizedBox(height: 24), // Space between day groups
          ],
        );
      },
    );
  }

  Map<DateTime, List<dynamic>> _groupJournalsByDate(List<dynamic> journals) {
    final Map<DateTime, List<dynamic>> grouped = {};

    for (final journal in journals) {
      if (journal.createdAt != null) {
        final date = DateTime(
          journal.createdAt!.year,
          journal.createdAt!.month,
          journal.createdAt!.day,
        );

        if (!grouped.containsKey(date)) {
          grouped[date] = [];
        }
        grouped[date]!.add(journal);
      }
    }

    // Sort journals within each day by time (most recent first)
    for (final date in grouped.keys) {
      grouped[date]!.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
    }

    return grouped;
  }

  Widget _buildDayHeader(DateTime date, int entryCount) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    String dayText;
    if (date == today) {
      dayText = 'Today';
    } else if (date == yesterday) {
      dayText = 'Yesterday';
    } else {
      dayText = DateFormat('EEEE, MMMM d, y').format(date);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.auto_stories, color: AppTheme.apricot, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              dayText,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.blueberry,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.citrus.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$entryCount',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalCard(dynamic journalEntry) {
    return Dismissible(
      background: Container(
        color: AppTheme.citrus,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      key: Key(journalEntry.id.toString()),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) async {
        await di<JournalController>().deleteJournalEntry(journalEntry.id);
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Image at the top if available
            if (journalEntry.imagePath != null &&
                journalEntry.imagePath!.isNotEmpty)
              SizedBox(
                width: double.infinity,
                child: Image.file(
                  File(journalEntry.imagePath!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.grey[400],
                            size: 50,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Image not found',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

            // Content section
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time only (since date is in header)
                  if (journalEntry.createdAt != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.blueberry.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: AutoSizeText(
                        DateFormat('h:mm a').format(journalEntry.createdAt!),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.blueberry,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  const SizedBox(height: 12),

                  // Note content
                  AutoSizeText(
                    journalEntry.note,
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(height: 1.5, fontSize: 22),
                  ),

                  const SizedBox(height: 12),

                  // Bottom row with AI sharing indicator
                  Row(
                    children: [
                      if (journalEntry.isSharedWithAI) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.citrus.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.psychology,
                                size: 16,
                                color: AppTheme.citrus,
                              ),
                              const SizedBox(width: 4),
                              AutoSizeText(
                                'Shared with AI',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.citrus,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      const Spacer(),
                      // Entry ID (for debugging/reference)
                      Text(
                        'Entry #${journalEntry.id}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Beautiful journal icon with gradient background
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.blueberry.withValues(alpha: 0.8),
                    AppTheme.apricot.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.blueberry.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.auto_stories,
                size: 60,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 32),

            // Main title
            Text(
              'Start Your Journal Journey',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Subtitle
            Text(
              'Capture your thoughts, memories, and moments.\nYour personal space to reflect and grow.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Feature highlights
            Column(
              children: [
                _buildFeatureItem(
                  context,
                  Icons.edit_note,
                  'Write your thoughts',
                  AppTheme.citrus,
                ),
                const SizedBox(height: 16),
                _buildFeatureItem(
                  context,
                  Icons.photo_camera,
                  'Add photos to memories',
                  AppTheme.apricot,
                ),
                const SizedBox(height: 16),
                _buildFeatureItem(
                  context,
                  Icons.mic,
                  'Use voice-to-text',
                  AppTheme.blueberry,
                ),
              ],
            ),
            const SizedBox(height: 40),
            Text(
              'Tap the + button to add your first mood',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String text,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
