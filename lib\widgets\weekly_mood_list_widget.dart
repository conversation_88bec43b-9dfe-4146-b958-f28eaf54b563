import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class WeeklyMoodListWidget extends StatelessWidget {
  final List<MoodModel> moods;

  const WeeklyMoodListWidget({super.key, required this.moods});

  @override
  Widget build(BuildContext context) {
    final weeklyMoodData = _calculateWeeklyMoodData();

    if (weeklyMoodData.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.citrus.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.mood_rounded,
                    color: AppTheme.blueberry,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Weekly Mood Summary',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.blueberry,
                        ),
                      ),
                      Text(
                        _getWeekDateRange(),
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.blueberry.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.apricot.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${_getTotalMoodCount()} moods',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.apricot,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppTheme.citrus.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            itemCount: weeklyMoodData.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final moodData = weeklyMoodData[index];
              final isTopMood = index == 0;

              return Container(
                decoration: BoxDecoration(
                  color:
                      isTopMood
                          ? moodData.color.withValues(alpha: 0.15)
                          : moodData.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: moodData.color.withValues(alpha: 0.3),
                    width: isTopMood ? 2 : 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Mood image
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: moodData.color.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Image.asset(
                            moodData.imagePath,
                            width: 40,
                            height: 40,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Mood info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              moodData.name,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.blueberry,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              moodData.count == 1
                                  ? '1 time this week'
                                  : '${moodData.count} times this week',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.blueberry.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                            if (isTopMood) ...[
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.blueberry.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Most frequent',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.blueberry,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Count badge
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: moodData.color,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: moodData.color.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            '${moodData.count}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.citrus.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.mood_rounded,
                size: 48,
                color: AppTheme.blueberry.withValues(alpha: 0.5),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'No Moods This Week',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start tracking your moods to see your weekly summary here!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.blueberry.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<WeeklyMoodData> _calculateWeeklyMoodData() {
    final Map<String, int> moodCounts = {};
    final now = DateTime.now();

    // Calculate start of current week (Monday)
    final startOfWeek = DateTime(
      now.year,
      now.month,
      now.day - now.weekday + 1,
    );
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    // Count moods for this week
    for (final mood in moods) {
      final moodDate = mood.createdAt ?? DateTime.now();

      // Check if mood is within current week
      if (moodDate.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
          moodDate.isBefore(endOfWeek.add(const Duration(days: 1))) &&
          mood.mood != null &&
          mood.mood!.isNotEmpty) {
        moodCounts[mood.mood!] = (moodCounts[mood.mood!] ?? 0) + 1;
      }
    }

    // Convert to list and sort by count (descending)
    final moodList =
        moodCounts.entries
            .map(
              (entry) => WeeklyMoodData(
                name: entry.key,
                count: entry.value,
                color: _getMoodColor(entry.key),
                imagePath: _getMoodImagePath(entry.key),
              ),
            )
            .toList();

    moodList.sort((a, b) => b.count.compareTo(a.count));
    return moodList;
  }

  String _getWeekDateRange() {
    final now = DateTime.now();
    final startOfWeek = DateTime(
      now.year,
      now.month,
      now.day - now.weekday + 1,
    );
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    final formatter = DateFormat('MMM d');
    return '${formatter.format(startOfWeek)} - ${formatter.format(endOfWeek)}';
  }

  int _getTotalMoodCount() {
    final weeklyMoodData = _calculateWeeklyMoodData();
    return weeklyMoodData.fold(0, (sum, mood) => sum + mood.count);
  }

  Color _getMoodColor(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return const Color(0xFFFF0000); // Red
      case 'bad':
        return const Color(0xFFFF5600); // Orange
      case 'ok':
        return const Color(0xFF0070FF); // Blue
      case 'happy':
        return const Color(0xFFFFC800); // Yellow
      case 'joy':
        return const Color(0xFF25FF2E); // Green
      default:
        return AppTheme.blueberry;
    }
  }

  String _getMoodImagePath(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return 'assets/images/mad.png';
      case 'bad':
        return 'assets/images/bad.png';
      case 'ok':
        return 'assets/images/ok.png';
      case 'happy':
        return 'assets/images/happy.png';
      case 'joy':
        return 'assets/images/joy.png';
      default:
        return 'assets/images/ok.png';
    }
  }
}

class WeeklyMoodData {
  final String name;
  final int count;
  final Color color;
  final String imagePath;

  WeeklyMoodData({
    required this.name,
    required this.count,
    required this.color,
    required this.imagePath,
  });
}
