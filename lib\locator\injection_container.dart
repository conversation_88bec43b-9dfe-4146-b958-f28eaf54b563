import 'package:firebase_auth/firebase_auth.dart';
import 'package:objectbox/objectbox.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/auth_controller.dart';
import '../controllers/chat_controller.dart';
import '../controllers/journal_contoller.dart';
import '../controllers/mood_controller.dart';
import '../controllers/notifications_controller.dart';
import '../controllers/self_help_tips_controller.dart';
import '../controllers/settings_controller.dart';
import '../controllers/theme_mode_controller.dart';
import '../models/activities_model.dart';
import '../models/jounal.dart';
import '../models/mood.dart';
import '../repositories/add_activities_local_db.dart';
import '../repositories/add_jounal_local_db.dart';
import '../repositories/add_mood_local_db.dart';
import '../repositories/audio_server_api.dart';
import '../repositories/auth_repository.dart';
import '../repositories/backend_api.dart';
import '../repositories/dark_mode_local_db.dart';
import '../repositories/i_add_activities_local_db.dart';
import '../repositories/i_add_jounal_local_db.dart';
import '../repositories/i_add_mood_local_db.dart';
import '../repositories/i_audio_server_api.dart';
import '../repositories/i_auth_repository.dart';
import '../repositories/i_backend_api.dart';
import '../repositories/i_dark_mode_local_db.dart';
import '../repositories/i_notifications_prefs.dart';
import '../repositories/notifications_prefs.dart';
import '../services/analytics_service.dart';
import '../services/flutter_notifications_service.dart';
import '../store/payment_popup.dart';
import '../utilities/snack_bar_show.dart';

initializeDependencies(Store store) {
  di.registerSingleton<Store>(store);

  di.registerSingleton<Box<MoodModel>>(store.box<MoodModel>());

  di.registerSingleton<Box<ActivitiesModel>>(store.box<ActivitiesModel>());

  di.registerLazySingleton<IAddActivitiesLocalDb>(
    () => AddActivitiesLocalDb(di<Box<ActivitiesModel>>()),
  );

  di.registerLazySingleton<IAddMoodLocalDb>(
    () => AddMoodLocalDb(di<Box<MoodModel>>()),
  );

  di.registerSingleton<Box<JournalModel>>(store.box<JournalModel>());

  di.registerLazySingleton<IAddJournalLocalDb>(
    () => AddJournalLocalDb(di<Box<JournalModel>>()),
  );

  di.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);

  di.registerLazySingleton<IAuthRepository>(
    () => AuthRepository(di<FirebaseAuth>()),
  );

  // Auth Controller
  di.registerFactory<AuthController>(
    () => AuthController(di<IAuthRepository>()),
  );

  di.registerSingleton<SnackBarShow>(SnackBarShow());

  di.registerLazySingleton<MoodController>(() => MoodController());

  di.registerLazySingleton<IDarkModeLocalDb>(() => DarkModeLocalDb());

  di.registerSingleton<ThemeModeController>(ThemeModeController());

  di.registerLazySingleton<JournalController>(() => JournalController());

  di.registerLazySingleton<ChatController>(() => ChatController());

  di.registerLazySingleton<AnalyticsService>(() => AnalyticsService());

  di.registerLazySingleton<IBackendApi>(() => BackendApi());

  di.registerLazySingleton<INotificationsPrefs>(() => NotificationsPrefs());

  di.registerLazySingleton<FlutterNotificationsService>(
    () => FlutterNotificationsService(),
  );

  di.registerLazySingleton<NotificationsController>(
    () => NotificationsController(),
  );

  di.registerLazySingleton<SettingsController>(() => SettingsController());

  di.registerLazySingleton<SelfHelpTipsController>(
    () => SelfHelpTipsController(),
  );

  di.registerLazySingleton<IAudioServerApi>(() => AudioServerApi());

  di.registerLazySingleton<PaymentPopup>(() => PaymentPopup());
}
