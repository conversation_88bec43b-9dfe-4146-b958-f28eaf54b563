import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/self_help_tips_controller.dart';
import '../../theme/app_theme.dart';
import '../../widgets/self_help_card.dart';

class RelaxUnwindScreen extends StatelessWidget with WatchItMixin {
  const RelaxUnwindScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final self = watchIt<SelfHelpTipsController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Relax & Unwind'), elevation: 0),
      body:
          self.isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.only(
                  top: 16,
                  left: 16,
                  right: 16,
                  bottom: 80,
                ),
                children: [
                  InkWell(
                    onTap: () {
                      context.push('/selfHelpTipsDetail/Audio Meditations');
                    },
                    child: SelfHelpCard(
                      firstColor: AppTheme.citrus,
                      secondColor: AppTheme.apricot,
                      title: 'Audio Guided Meditations',
                      description:
                          'Guided meditations to help you relax and unwind.',
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      context.push('/selfHelpTipsDetail/Peaceful Sounds');
                    },
                    child: SelfHelpCard(
                      firstColor: AppTheme.citrus,
                      secondColor: AppTheme.apricot,
                      title: 'Peaceful Sounds',
                      description:
                          'Nature sounds to help you relax and unwind.',
                    ),
                  ),
                ],
              ),
    );
  }
}
