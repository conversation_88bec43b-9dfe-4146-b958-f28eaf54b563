import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../theme/app_theme.dart';
import '../../theme/text_sizes.dart';

class AIInfoScreen extends StatelessWidget {
  const AIInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('AI Information'), elevation: 0),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON><PERSON>iew(
          children: [
            const AutoSizeText(
              'Why Share with AI?',
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'Sharing your mood with AI can help you better understand your mental health. The AI can provide insights and recommendations based on your mood rating data.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              'Data Security',
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'We take your data privacy seriously. The AI only accesses aggregated and anonymized data to provide insights. There is no personal identifiable information shared with the AI. Just the mood ratings.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'The aggregated and anonymized data is delete after 74 hours.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'The app\'s data, moods & journal entries is stored locally on your device and is not shared with any third parties.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              'AI Usage',
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'The AI is used to analyze your mood ratings to provide personalized insights and recommendations. It does not have access to your personal information.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              'Turning Off AI',
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const AutoSizeText(
              'If you do not want to share your mood rating data with the AI, you can turn off the "Share with AI" option when adding a mood. Your data will not be sent to the AI and will only be used locally on your device.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            const AutoSizeText(
              'By using this app, you agree to our terms of service and privacy policy.',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 40),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _launchUrl(Uri.parse('https://moodvibe.org/privacy'));
                },
                child: Text(
                  'Read More',
                  style: TextStyle(fontSize: TextSizes.buttonText),
                ),
              ),
            ),
            const SizedBox(height: 80),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(url) async {
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
