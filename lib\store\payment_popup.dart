import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';

class PaymentPopup {
  Future<bool> showPaywall() async {
    bool proUser = false;
    try {
      CustomerInfo customerInfo = await Purchases.getCustomerInfo();
      proUser = customerInfo.entitlements.all['ProOptions']?.isActive ?? false;

      if (!proUser) {
        await RevenueCatUI.presentPaywallIfNeeded("MoodVide1");
        // Check again after presenting paywall
        customerInfo = await Purchases.getCustomerInfo();
        proUser =
            customerInfo.entitlements.all['ProOptions']?.isActive ?? false;
      }
    } on PlatformException catch (e) {
      debugPrint('Platform Exception in showPaywall: ${e.message}');
    } catch (e) {
      debugPrint('Error in showPaywall: $e');
    }
    return proUser;
  }

  Future<bool> isProUser() async {
    bool proUser = false;
    try {
      CustomerInfo customerInfo = await Purchases.getCustomerInfo();
      proUser = customerInfo.entitlements.all['ProOptions']?.isActive ?? false;
    } on PlatformException catch (e) {
      debugPrint('Platform Exception in isProUser: ${e.message}');
    } catch (e) {
      debugPrint('Error in isProUser: $e');
    }
    return proUser;
  }
}
