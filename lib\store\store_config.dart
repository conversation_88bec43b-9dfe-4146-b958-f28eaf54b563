enum MyStore { appleStore, googlePlay }

class StoreConfig {
  final MyStore store;
  final String apiKey;
  static StoreConfig? _instance;

  factory StoreConfig({required MyStore store, required String apiKey}) {
    _instance ??= StoreConfig._internal(store, apiKey);
    return _instance!;
  }

  StoreConfig._internal(this.store, this.apiKey);

  static StoreConfig get instance {
    return _instance!;
  }

  static bool isForAppleStore() => instance.store == MyStore.appleStore;

  static bool isForGooglePlay() => instance.store == MyStore.googlePlay;
}
