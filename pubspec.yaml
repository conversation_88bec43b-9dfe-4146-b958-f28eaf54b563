name: moodvibe
description: "moodvibe"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # cupertino_icons: ^1.0.8
  go_router: ^15.1.2
  watch_it: ^1.6.5
  emoji_rating_bar: ^0.0.2
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  objectbox: ^4.2.0
  objectbox_flutter_libs: ^4.2.0
  path_provider: ^2.1.5
  path: ^1.9.1
  shared_preferences: ^2.5.3
  intl: ^0.19.0
  fl_chart: ^1.0.0
  image_picker: ^1.1.2
  flutter_launcher_icons: ^0.14.3
  speech_to_text: ^7.0.0
  auto_size_text: ^3.0.0
  dio: ^5.8.0+1
  dash_chat_2: ^0.0.21
  loading_indicator: ^3.1.1
  firebase_analytics: ^11.4.6
  flutter_local_notifications: ^18.0.1
  timezone: ^0.10.1
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  just_audio: ^0.10.4
  audio_session: ^0.1.21
  purchases_flutter: ^8.10.5
  purchases_ui_flutter: ^8.10.5
  audioplayers: ^6.5.0
  

dev_dependencies:
  flutter_test:
    sdk: flutter


  objectbox_generator: ^4.2.0
  build_runner: ^2.4.15
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
         

  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
