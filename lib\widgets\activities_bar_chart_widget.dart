import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class ActivitiesBarChartWidget extends StatelessWidget {
  final List<MoodModel> moods;
  final int daysToShow;

  const ActivitiesBarChartWidget({
    super.key,
    required this.moods,
    this.daysToShow = 10,
  });

  @override
  Widget build(BuildContext context) {
    final chartData = _prepareChartData();

    // Check if there are any activities
    final hasActivities = chartData.any((data) => data.count > 0);

    if (!hasActivities) {
      return _buildEmptyState();
    }

    return Container(
      height: 350,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity Trends (Last $daysToShow Days)',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: _getMaxCount(chartData) + 2,
                barTouchData: BarTouchData(
                  enabled: true,
                  touchTooltipData: BarTouchTooltipData(
                    getTooltipColor:
                        (group) => AppTheme.blueberry.withValues(alpha: 0.8),
                    tooltipBorderRadius: BorderRadius.circular(8),
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      final data = chartData[group.x.toInt()];
                      return BarTooltipItem(
                        '${DateFormat('MMM dd').format(data.date)}\n${data.count} activities',
                        const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      );
                    },
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        if (value.toInt() >= 0 &&
                            value.toInt() < chartData.length) {
                          final data = chartData[value.toInt()];
                          return Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  DateFormat('dd').format(data.date),
                                  style: TextStyle(
                                    color: AppTheme.blueberry.withValues(
                                      alpha: 0.7,
                                    ),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  DateFormat('MMM').format(data.date),
                                  style: TextStyle(
                                    color: AppTheme.blueberry.withValues(
                                      alpha: 0.5,
                                    ),
                                    fontSize: 10,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const Text('');
                      },
                      reservedSize: 40,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          value.toInt().toString(),
                          style: TextStyle(
                            color: AppTheme.blueberry.withValues(alpha: 0.7),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _generateBarGroups(chartData),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: AppTheme.blueberry.withValues(alpha: 0.1),
                      strokeWidth: 1,
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_activity_outlined,
            size: 48,
            color: AppTheme.blueberry.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Activities Yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start adding activities to your moods\nto see your activity trends!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.blueberry.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.apricot, AppTheme.citrus],
            ),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Activity Count',
          style: TextStyle(
            color: AppTheme.blueberry.withValues(alpha: 0.8),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  List<ActivityChartData> _prepareChartData() {
    final List<ActivityChartData> chartData = [];
    final now = DateTime.now();

    // Generate last N days
    for (int i = daysToShow - 1; i >= 0; i--) {
      final date = DateTime(now.year, now.month, now.day - i);

      // Find moods with activities for this date
      final dayMoodsWithActivities =
          moods.where((mood) {
            final moodDate = mood.createdAt ?? DateTime.now();
            return moodDate.year == date.year &&
                moodDate.month == date.month &&
                moodDate.day == date.day &&
                mood.activity != null &&
                mood.activity!.isNotEmpty;
          }).toList();

      chartData.add(
        ActivityChartData(date: date, count: dayMoodsWithActivities.length),
      );
    }

    return chartData;
  }

  List<BarChartGroupData> _generateBarGroups(
    List<ActivityChartData> chartData,
  ) {
    return chartData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: data.count.toDouble(),
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [AppTheme.apricot, AppTheme.citrus],
            ),
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(6),
              topRight: Radius.circular(6),
            ),
            backDrawRodData: BackgroundBarChartRodData(
              show: true,
              toY: _getMaxCount(chartData) + 2,
              color: AppTheme.appleCore.withValues(alpha: 0.1),
            ),
          ),
        ],
        showingTooltipIndicators: [],
      );
    }).toList();
  }

  double _getMaxCount(List<ActivityChartData> chartData) {
    if (chartData.isEmpty) return 5;
    final maxCount = chartData
        .map((data) => data.count)
        .reduce((a, b) => a > b ? a : b);
    return maxCount.toDouble();
  }
}

class ActivityChartData {
  final DateTime date;
  final int count;

  ActivityChartData({required this.date, required this.count});
}
