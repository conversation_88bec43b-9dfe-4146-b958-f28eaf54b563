import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/self_help_tips_controller.dart';
import '../../models/audio_model.dart';
import '../../theme/app_theme.dart';

import '../../widgets/audioplayers_bottom_sheet.dart';

class SelfHelpTipsDetailScreen extends WatchingStatefulWidget {
  final String? screen;

  const SelfHelpTipsDetailScreen({super.key, this.screen});

  @override
  State<SelfHelpTipsDetailScreen> createState() =>
      _SelfHelpTipsDetailScreenState();
}

class _SelfHelpTipsDetailScreenState extends State<SelfHelpTipsDetailScreen> {
  /// Cleans audio title by removing .mp3 extension, numerals, and capitalizing words
  String _cleanAudioTitle(String title) {
    // Remove .mp3 extension (case insensitive)
    String cleaned = title.replaceAll(
      RegExp(r'\.mp3$', caseSensitive: false),
      '',
    );

    // Remove leading numerals and common separators (e.g., "01 - ", "1. ", "001_")
    cleaned = cleaned.replaceAll(RegExp(r'^\d+[\s\-_.]*'), '');

    // Remove trailing numerals (e.g., "Sleep1" -> "Sleep")
    cleaned = cleaned.replaceAll(RegExp(r'\d+$'), '');

    // Replace underscores and hyphens with spaces
    cleaned = cleaned.replaceAll(RegExp(r'[_-]'), ' ');

    // Remove extra spaces
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Capitalize each word
    return cleaned
        .split(' ')
        .map((word) {
          if (word.isEmpty) return word;
          return word[0].toUpperCase() + word.substring(1).toLowerCase();
        })
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    late List<AudioModel> self;

    if (widget.screen == 'Audio Meditations') {
      self = watchIt<SelfHelpTipsController>().selfHelpTipsAudio;
    } else if (widget.screen == 'Peaceful Sounds') {
      self = watchIt<SelfHelpTipsController>().peacefulSoundsAudio;
    } else {
      self = watchIt<SelfHelpTipsController>().selfHelpTipsAudio;
    }

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: Text('${widget.screen}'), elevation: 0),
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 18, right: 18, top: 8),
              child: AutoSizeText(
                "This section will be updated weekly with new content.",
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.5,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: self.length,
                padding: const EdgeInsets.only(
                  top: 8,
                  left: 8,
                  right: 8,
                  bottom: 80,
                ),
                itemBuilder: (context, index) {
                  final tip = self[index];
                  return InkWell(
                    onTap: () {
                      if (tip.audioFileUrl != null &&
                          tip.audioFileUrl!.isNotEmpty) {
                        // Use AudioPlayers for iOS, just_audio for Android

                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          builder:
                              (context) => AudioPlayersBottomSheet(
                                audioUrl: tip.audioFileUrl!,
                                title: _cleanAudioTitle(
                                  tip.name ?? 'Audio Track',
                                ),
                              ),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Audio not available for this track'),
                          ),
                        );
                      }
                    },
                    child: Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Theme.of(context).brightness == Brightness.light
                                  ? AppTheme.citrus.withValues(alpha: 0.05)
                                  : AppTheme.citrus.withValues(alpha: 0.1),
                              Theme.of(context).brightness == Brightness.light
                                  ? AppTheme.appleCore.withValues(alpha: 0.03)
                                  : AppTheme.appleCore.withValues(alpha: 0.05),
                            ],
                          ),
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 8,
                          ),
                          leading: Container(
                            width: 40,
                            height: 0,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [AppTheme.citrus, AppTheme.apricot],
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                "${index + 1}",
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          title: Text(
                            _cleanAudioTitle(tip.name ?? ''),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          subtitle: Text(
                            'Tap to play audio',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.citrus.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.play_arrow,
                              color: AppTheme.citrus,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
