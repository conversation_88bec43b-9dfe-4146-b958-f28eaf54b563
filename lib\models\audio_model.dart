class AudioModel {
  String? name;
  String? audioFileUrl;

  AudioModel({this.name, this.audioFileUrl});

  AudioModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    audioFileUrl = json['audioFileUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['audioFileUrl'] = audioFileUrl;
    return data;
  }
}
